// Environment configuration
export const config = {
  // API URLs
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'https://sumopod-backend.fly.dev',
  goldApiUrl: import.meta.env.VITE_GOLD_API_URL || 'https://logam-mulia-api.vercel.app/prices/anekalogam',
} as const;

// Type-safe environment variable access
export const getEnvVar = (key: keyof typeof config): string => {
  const value = config[key];
  if (!value) {
    throw new Error(`Environment variable ${key} is not defined`);
  }
  return value;
};
